from fastapi import APIRouter, UploadFile, File, HTTPException, Depends
from fastapi.responses import FileResponse
from typing import List
from ..services.font_service import FontService
from ..storage.file_storage import FileStorage

router = APIRouter(prefix="/fonts", tags=["fonts"])

# 依赖注入
def get_font_service() -> FontService:
    storage = FileStorage()
    return FontService(storage)

@router.get("/")
async def get_fonts(font_service: FontService = Depends(get_font_service)):
    """获取所有字体列表"""
    return font_service.get_fonts()

@router.post("/upload")
async def upload_font(
    file: UploadFile = File(...),
    font_service: FontService = Depends(get_font_service)
):
    """上传字体文件"""
    return await font_service.upload_font(file)

@router.get("/{font_id}/download")
async def download_font(
    font_id: str,
    font_service: FontService = Depends(get_font_service)
):
    """下载字体文件"""
    file_path = font_service.get_font_file_path(font_id)
    
    if not file_path:
        raise HTTPException(status_code=404, detail="字体文件不存在")
    
    # 获取字体信息以获取原始文件名
    font_info = font_service.get_font_info(font_id)
    filename = font_info.filename if font_info else font_id
    
    return FileResponse(
        path=file_path,
        filename=filename,
        media_type='application/octet-stream'
    )

@router.delete("/{font_id}")
async def delete_font(
    font_id: str,
    font_service: FontService = Depends(get_font_service)
):
    """删除字体"""
    return font_service.delete_font(font_id)

@router.get("/{font_id}")
async def get_font_info(
    font_id: str,
    font_service: FontService = Depends(get_font_service)
):
    """获取字体信息"""
    font_info = font_service.get_font_info(font_id)
    
    if not font_info:
        raise HTTPException(status_code=404, detail="字体不存在")
    
    return {
        "success": True,
        "data": font_info.dict(),
        "message": "获取字体信息成功"
    }
