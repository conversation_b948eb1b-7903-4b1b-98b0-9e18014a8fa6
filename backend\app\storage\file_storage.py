import json
import os
import shutil
import uuid
from datetime import datetime
from pathlib import Path
from typing import List, Optional, Dict, Any
import aiofiles

class FileStorage:
    """本地文件存储管理器"""
    
    def __init__(self, base_dir: str = "."):
        self.base_dir = Path(base_dir)
        self.data_dir = self.base_dir / "data"
        self.fonts_dir = self.base_dir / "fonts"
        
        # 确保目录存在
        self.data_dir.mkdir(exist_ok=True)
        self.fonts_dir.mkdir(exist_ok=True)
        
        # 数据文件路径
        self.templates_file = self.data_dir / "templates.json"
        self.configs_file = self.data_dir / "configs.json"
        self.fonts_file = self.data_dir / "fonts.json"
        
        # 初始化数据文件
        self._init_data_files()
    
    def _init_data_files(self):
        """初始化数据文件"""
        # 初始化模板文件
        if not self.templates_file.exists():
            default_templates = [
                {
                    "id": "tianzige",
                    "name": "田字格",
                    "description": "标准田字格，适合汉字练习",
                    "config": {
                        "name": "田字格-标准",
                        "grid_type": "tianzige",
                        "rows": 10,
                        "cols": 7,
                        "cell_w_mm": 20,
                        "cell_h_mm": 20,
                        "margin_top_mm": 15,
                        "margin_left_mm": 15,
                        "line_style": "solid",
                        "line_color": "#CCCCCC",
                        "include_mid_guides": True
                    }
                },
                {
                    "id": "mizige",
                    "name": "米字格",
                    "description": "米字格，带对角线辅助",
                    "config": {
                        "name": "米字格-标准",
                        "grid_type": "mizige",
                        "rows": 10,
                        "cols": 7,
                        "cell_w_mm": 20,
                        "cell_h_mm": 20,
                        "margin_top_mm": 15,
                        "margin_left_mm": 15,
                        "line_style": "solid",
                        "line_color": "#CCCCCC",
                        "include_mid_guides": True
                    }
                }
            ]
            self._write_json(self.templates_file, default_templates)
        
        # 初始化配置文件
        if not self.configs_file.exists():
            self._write_json(self.configs_file, [])
        
        # 初始化字体文件
        if not self.fonts_file.exists():
            default_fonts = [
                {
                    "id": "kaiti.ttf",
                    "name": "楷体",
                    "filename": "kaiti.ttf",
                    "size": 0,
                    "uploaded_at": datetime.now().isoformat()
                }
            ]
            self._write_json(self.fonts_file, default_fonts)
    
    def _read_json(self, file_path: Path) -> List[Dict[str, Any]]:
        """读取 JSON 文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except (FileNotFoundError, json.JSONDecodeError):
            return []
    
    def _write_json(self, file_path: Path, data: List[Dict[str, Any]]):
        """写入 JSON 文件"""
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
    
    # 模板管理
    def get_templates(self) -> List[Dict[str, Any]]:
        """获取所有模板"""
        return self._read_json(self.templates_file)
    
    def save_template(self, template: Dict[str, Any]) -> Dict[str, Any]:
        """保存模板"""
        templates = self.get_templates()
        template_id = template.get("id") or str(uuid.uuid4())
        template["id"] = template_id
        
        # 查找是否已存在
        for i, t in enumerate(templates):
            if t["id"] == template_id:
                templates[i] = template
                break
        else:
            templates.append(template)
        
        self._write_json(self.templates_file, templates)
        return template
    
    def delete_template(self, template_id: str) -> bool:
        """删除模板"""
        templates = self.get_templates()
        original_length = len(templates)
        templates = [t for t in templates if t["id"] != template_id]
        
        if len(templates) < original_length:
            self._write_json(self.templates_file, templates)
            return True
        return False
    
    # 配置管理
    def get_configs(self) -> List[Dict[str, Any]]:
        """获取所有配置"""
        return self._read_json(self.configs_file)
    
    def save_config(self, config: Dict[str, Any]) -> str:
        """保存配置"""
        configs = self.get_configs()
        config_id = str(uuid.uuid4())
        
        config_info = {
            "id": config_id,
            "name": config.get("meta", {}).get("created_by", "未命名配置"),
            "created_at": datetime.now().isoformat(),
            "config": config
        }
        
        configs.append(config_info)
        self._write_json(self.configs_file, configs)
        return config_id
    
    def get_config(self, config_id: str) -> Optional[Dict[str, Any]]:
        """获取指定配置"""
        configs = self.get_configs()
        for config in configs:
            if config["id"] == config_id:
                return config["config"]
        return None
    
    def delete_config(self, config_id: str) -> bool:
        """删除配置"""
        configs = self.get_configs()
        original_length = len(configs)
        configs = [c for c in configs if c["id"] != config_id]
        
        if len(configs) < original_length:
            self._write_json(self.configs_file, configs)
            return True
        return False
    
    # 字体管理
    def get_fonts(self) -> List[Dict[str, Any]]:
        """获取所有字体"""
        return self._read_json(self.fonts_file)
    
    def save_font_info(self, font_info: Dict[str, Any]) -> Dict[str, Any]:
        """保存字体信息"""
        fonts = self.get_fonts()
        font_info["uploaded_at"] = datetime.now().isoformat()
        
        # 检查是否已存在
        for i, f in enumerate(fonts):
            if f["id"] == font_info["id"]:
                fonts[i] = font_info
                break
        else:
            fonts.append(font_info)
        
        self._write_json(self.fonts_file, fonts)
        return font_info
    
    def delete_font_info(self, font_id: str) -> bool:
        """删除字体信息"""
        fonts = self.get_fonts()
        original_length = len(fonts)
        fonts = [f for f in fonts if f["id"] != font_id]
        
        if len(fonts) < original_length:
            self._write_json(self.fonts_file, fonts)
            return True
        return False
    
    async def save_font_file(self, file_content: bytes, filename: str) -> str:
        """保存字体文件"""
        file_id = f"{uuid.uuid4()}_{filename}"
        file_path = self.fonts_dir / file_id
        
        async with aiofiles.open(file_path, 'wb') as f:
            await f.write(file_content)
        
        return file_id
    
    def get_font_file_path(self, file_id: str) -> Optional[Path]:
        """获取字体文件路径"""
        file_path = self.fonts_dir / file_id
        return file_path if file_path.exists() else None
    
    def delete_font_file(self, file_id: str) -> bool:
        """删除字体文件"""
        file_path = self.fonts_dir / file_id
        if file_path.exists():
            file_path.unlink()
            return True
        return False
