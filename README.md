# 字帖生成器 (Copybook Generator)

一个现代化的可打印字帖生成器，支持多种模板、自定义字体、配置导入导出等功能。

## 🚀 功能特性

- 📝 **多种字帖模板**: 田字格、米字格、九宫格、拼音四线三格、英文双线等
- 🎨 **可视化编辑**: 拖拽调整布局、实时预览
- 🔤 **字体管理**: 支持上传自定义字体文件
- 📄 **PDF导出**: 高质量PDF生成，支持A4/A3页面
- ⚙️ **配置导入**: 支持JSON配置文件批量生成
- 🎯 **高级功能**: 水印、二维码、拼音标注、多页批量生成

## 🏗️ 技术栈

### 前端
- React 18 + TypeScript
- Vite (构建工具)
- TailwindCSS (样式)
- Zustand (状态管理)
- jsPDF (PDF生成)

### 后端
- FastAPI (Python)
- 本地JSON存储
- 静态文件服务

## 📦 项目结构

```
字帖/
├── frontend/                 # React前端应用
├── backend/                  # FastAPI后端服务
├── docs/                     # 项目文档
└── README.md
```

## 🚀 快速开始

### 方式一：使用启动脚本（推荐）
```bash
# Windows 用户
start-dev.bat

# 或者手动启动
```

### 方式二：手动启动

#### 前端开发
```bash
cd frontend
npm install
npm run dev
```
前端服务器将在 http://localhost:5173 启动

#### 后端开发
```bash
cd backend
pip install -r requirements.txt
python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```
后端服务器将在 http://localhost:8000 启动

### 访问地址
- 🎨 **前端应用**: http://localhost:5173
- � **后端 API**: http://localhost:8000
- 📚 **API 文档**: http://localhost:8000/docs
- 📖 **ReDoc 文档**: http://localhost:8000/redoc

## �📋 开发计划

- [x] 项目初始化
- [x] 前端基础框架搭建
- [x] 后端API接口开发
- [x] 字帖编辑器组件
- [x] 模板系统实现
- [x] 字体管理功能
- [x] PDF导出功能
- [x] 配置导入导出
- [x] 高级功能集成

## 🎯 核心功能

### ✅ 已完成功能

1. **字帖编辑器**
   - 可视化编辑界面
   - 实时预览
   - 多种模板支持（田字格、米字格、九宫格等）
   - 自定义格子尺寸和布局

2. **模板系统**
   - 预设模板选择
   - 自定义模板保存
   - 模板参数调整（行数、列数、边距等）
   - 线条样式自定义

3. **字体管理**
   - 字体上传功能
   - 字体选择和预览
   - 字体大小调整

4. **内容编辑**
   - 文本内容输入
   - 拼音标注支持
   - 每字重复次数设置
   - 多页内容管理

5. **配置导入导出**
   - JSON 配置文件导入
   - 配置文件导出
   - 标准化配置格式

6. **PDF 导出**
   - 高质量 PDF 生成
   - A4/A3 页面支持
   - 多页批量导出
   - 打印功能

7. **高级功能**
   - 水印添加
   - 二维码集成
   - 页面导航
   - 预览模式切换

## 📁 项目结构详解

```
字帖/
├── frontend/                 # React 前端应用
│   ├── src/
│   │   ├── components/       # 组件目录
│   │   │   ├── CopybookCell.tsx      # 字帖单元格组件
│   │   │   ├── CopybookPreview.tsx   # 字帖预览组件
│   │   │   └── EditorPanel.tsx       # 编辑器面板组件
│   │   ├── stores/          # Zustand 状态管理
│   │   │   └── editorStore.ts        # 编辑器状态
│   │   ├── types/           # TypeScript 类型定义
│   │   │   └── index.ts              # 主要类型定义
│   │   ├── services/        # API 服务
│   │   │   └── api.ts                # API 调用封装
│   │   └── App.tsx          # 主应用组件
│   ├── package.json         # 前端依赖配置
│   └── tailwind.config.js   # TailwindCSS 配置
├── backend/                 # FastAPI 后端服务
│   ├── app/
│   │   ├── api/            # API 路由
│   │   │   ├── fonts.py            # 字体管理 API
│   │   │   ├── templates.py        # 模板管理 API
│   │   │   └── configs.py          # 配置管理 API
│   │   ├── models.py       # 数据模型定义
│   │   ├── services/       # 业务逻辑服务
│   │   │   ├── font_service.py     # 字体服务
│   │   │   ├── template_service.py # 模板服务
│   │   │   └── config_service.py   # 配置服务
│   │   ├── storage/        # 存储管理
│   │   │   └── file_storage.py     # 本地文件存储
│   │   └── main.py         # FastAPI 主应用
│   ├── data/               # JSON 数据文件存储
│   ├── fonts/              # 字体文件存储
│   ├── requirements.txt    # Python 依赖
│   └── example-config.json # 示例配置文件
├── start-dev.bat           # 开发环境启动脚本
└── README.md              # 项目说明文档
```

## 📖 使用说明

### 基本使用流程

1. **启动应用**
   - 运行 `start-dev.bat` 或手动启动前后端服务
   - 在浏览器中打开 http://localhost:5173

2. **编辑字帖内容**
   - 在"内容"标签页中输入要练习的文字
   - 设置每字重复次数
   - 如需拼音标注，选择拼音模板并输入拼音

3. **选择和调整模板**
   - 在"模板"标签页选择字帖类型（田字格、米字格等）
   - 调整行数、列数、格子尺寸
   - 自定义线条样式和颜色

4. **设置字体**
   - 在"字体"标签页选择字体
   - 调整字体大小
   - 可上传自定义字体文件

5. **多页管理**
   - 在"多页"标签页添加多个练习页面
   - 每页可设置不同的标题和内容

6. **导出和打印**
   - 点击"导出PDF"生成高质量PDF文件
   - 点击"打印"直接打印字帖
   - 点击"导出配置"保存当前设置

### 配置文件格式

系统支持标准 JSON 配置文件导入，格式如下：

```json
{
  "meta": {
    "version": "1.1",
    "created_by": "用户名或工具名",
    "created_at": "2025-06-04T12:30:00"
  },
  "template": {
    "name": "模板名称",
    "grid_type": "tianzige|mizige|jiugongge|pinyin|english|blank",
    "rows": 10,
    "cols": 7,
    "cell_w_mm": 20,
    "cell_h_mm": 20,
    "margin_top_mm": 15,
    "margin_left_mm": 15,
    "line_style": "solid|dashed|dotted",
    "line_color": "#CCCCCC",
    "include_mid_guides": true,
    "extra": {
      "assist_type": "mi|jiugong|none",
      "page_size": "A4|A3",
      "watermark": {
        "text": "水印文字",
        "opacity": 0.1,
        "position": "top-left|top-right|bottom-left|bottom-right|center"
      },
      "qrcode": {
        "url": "二维码链接",
        "position": "top-left|top-right|bottom-left|bottom-right",
        "size_mm": 18
      }
    }
  },
  "font": {
    "name": "字体名称",
    "file_id": "字体文件ID",
    "size_pt": 24
  },
  "content": {
    "text": "练习内容",
    "repeat_per_char": 2,
    "pinyin": ["可选的拼音数组"]
  },
  "multi_pages": [
    {
      "text": "页面内容",
      "title": "页面标题"
    }
  ]
}
```

### API 接口说明

后端提供 RESTful API 接口，主要包括：

- **字体管理**: `/api/fonts/`
  - GET: 获取字体列表
  - POST: 上传字体文件
  - DELETE: 删除字体

- **模板管理**: `/api/templates/`
  - GET: 获取模板列表
  - POST: 创建模板
  - PUT: 更新模板
  - DELETE: 删除模板

- **配置管理**: `/api/configs/`
  - GET: 获取配置列表
  - POST: 保存配置
  - GET /{id}: 获取指定配置
  - DELETE: 删除配置

详细 API 文档请访问：http://localhost:8000/docs

## 🔧 开发说明

### 技术栈选择

- **前端**: React 18 + TypeScript + Vite + TailwindCSS + Zustand
- **后端**: FastAPI + Python + Pydantic
- **PDF生成**: jsPDF + html2canvas
- **状态管理**: Zustand（轻量级，易于使用）
- **样式框架**: TailwindCSS（实用优先的CSS框架）

### 扩展开发

1. **添加新的字帖模板**
   - 在 `frontend/src/components/CopybookCell.tsx` 中添加新的格线渲染逻辑
   - 在 `frontend/src/types/index.ts` 中扩展 `grid_type` 类型
   - 在后端 `backend/app/models.py` 中同步更新类型定义

2. **添加新的导出格式**
   - 在 `frontend/src/services/api.ts` 中添加新的导出函数
   - 可以支持 PNG、SVG 等格式

3. **集成在线字体服务**
   - 扩展字体管理功能，支持 Google Fonts 等在线字体
   - 在 `FontService` 中添加在线字体获取逻辑

## 🤝 贡献

欢迎提交Issue和Pull Request！

## 📄 许可证

MIT License
