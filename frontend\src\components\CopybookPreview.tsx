import React, { useMemo } from 'react';
import CopybookCell from './CopybookCell';
import { useEditorStore } from '../stores/editorStore';
import type { CopybookCellProps } from '../types';

/**
 * 字帖预览组件
 * 显示完整的字帖页面，支持实时预览和缩放
 */
const CopybookPreview: React.FC = () => {
  const { config, currentPage } = useEditorStore();
  const { template, font, content, multi_pages } = config;

  // 计算当前页面的内容
  const currentContent = useMemo(() => {
    if (multi_pages && multi_pages.length > 0 && currentPage < multi_pages.length) {
      return multi_pages[currentPage];
    }
    return { text: content.text, title: '字帖练习' };
  }, [content.text, multi_pages, currentPage]);

  // 生成字帖格子数据
  const generateCells = useMemo((): CopybookCellProps[][] => {
    const text = currentContent.text;
    const chars = Array.from(text); // 支持 Unicode 字符
    const { rows, cols } = template;
    const cellsData: CopybookCellProps[][] = [];

    // 计算单元格尺寸（像素）
    const cellSize = {
      width: template.cell_w_mm * 3.78, // 1mm ≈ 3.78px (96 DPI)
      height: template.cell_h_mm * 3.78,
    };

    let charIndex = 0;

    for (let row = 0; row < rows; row++) {
      const rowCells: CopybookCellProps[] = [];
      
      for (let col = 0; col < cols; col++) {
        const char = chars[charIndex % chars.length] || '';
        const pinyin = content.pinyin?.[charIndex % (content.pinyin?.length || 1)];
        
        rowCells.push({
          char,
          pinyin,
          gridType: template.grid_type,
          cellSize,
          fontSize: font.size_pt,
          fontFamily: font.name,
          lineColor: template.line_color,
          lineStyle: template.line_style,
        });

        // 根据重复次数决定是否移动到下一个字符
        if ((col + 1) % content.repeat_per_char === 0) {
          charIndex++;
        }
      }
      
      cellsData.push(rowCells);
    }

    return cellsData;
  }, [template, font, content, currentContent.text]);

  // 计算页面样式
  const pageStyle: React.CSSProperties = {
    width: template.extra?.page_size === 'A3' ? '297mm' : '210mm',
    height: template.extra?.page_size === 'A3' ? '420mm' : '297mm',
    padding: `${template.margin_top_mm}mm ${template.margin_left_mm}mm`,
    backgroundColor: 'white',
    boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
    position: 'relative',
    margin: '0 auto',
  };

  // 渲染水印
  const renderWatermark = () => {
    if (!template.extra?.watermark) return null;

    const { text, opacity, position } = template.extra.watermark;
    
    const positionStyles: Record<string, React.CSSProperties> = {
      'top-left': { top: '20px', left: '20px' },
      'top-right': { top: '20px', right: '20px' },
      'bottom-left': { bottom: '20px', left: '20px' },
      'bottom-right': { bottom: '20px', right: '20px' },
      'center': { top: '50%', left: '50%', transform: 'translate(-50%, -50%)' },
    };

    return (
      <div
        style={{
          position: 'absolute',
          ...positionStyles[position],
          opacity,
          fontSize: '14px',
          color: '#999',
          pointerEvents: 'none',
          zIndex: 1,
        }}
      >
        {text}
      </div>
    );
  };

  // 渲染二维码
  const renderQRCode = () => {
    if (!template.extra?.qrcode) return null;

    const { url, position, size_mm } = template.extra.qrcode;
    
    const positionStyles: Record<string, React.CSSProperties> = {
      'top-left': { top: '10px', left: '10px' },
      'top-right': { top: '10px', right: '10px' },
      'bottom-left': { bottom: '10px', left: '10px' },
      'bottom-right': { bottom: '10px', right: '10px' },
    };

    return (
      <div
        style={{
          position: 'absolute',
          ...positionStyles[position],
          width: `${size_mm}mm`,
          height: `${size_mm}mm`,
          border: '1px solid #ccc',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          fontSize: '10px',
          color: '#666',
          zIndex: 1,
        }}
      >
        QR
      </div>
    );
  };

  return (
    <div className="copybook-preview bg-gray-100 p-8 min-h-screen">
      {/* 页面标题 */}
      <div className="text-center mb-4">
        <h2 className="text-xl font-semibold text-gray-800">
          {currentContent.title}
        </h2>
        {multi_pages && multi_pages.length > 1 && (
          <p className="text-sm text-gray-600">
            第 {currentPage + 1} 页 / 共 {multi_pages.length} 页
          </p>
        )}
      </div>

      {/* 字帖页面 */}
      <div style={pageStyle} className="copybook-page">
        {renderWatermark()}
        {renderQRCode()}
        
        {/* 字帖格子网格 */}
        <div className="grid-container">
          {generateCells.map((row, rowIndex) => (
            <div key={rowIndex} className="flex">
              {row.map((cellProps, colIndex) => (
                <CopybookCell
                  key={`${rowIndex}-${colIndex}`}
                  {...cellProps}
                />
              ))}
            </div>
          ))}
        </div>
      </div>

      {/* 页面信息 */}
      <div className="mt-4 text-center text-sm text-gray-500">
        <p>
          模板: {template.name} | 
          字体: {font.name} | 
          尺寸: {template.cell_w_mm}×{template.cell_h_mm}mm |
          页面: {template.extra?.page_size || 'A4'}
        </p>
      </div>
    </div>
  );
};

export default CopybookPreview;
