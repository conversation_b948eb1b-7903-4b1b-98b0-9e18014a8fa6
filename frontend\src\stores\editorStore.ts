import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import type { 
  EditorState, 
  CopybookConfig, 
  FontInfo, 
  TemplateInfo,
  CopybookTemplate,
  CopybookFont,
  CopybookContent 
} from '../types';

// 默认配置
const defaultConfig: CopybookConfig = {
  meta: {
    version: '1.1',
    created_by: 'USER',
    created_at: new Date().toISOString(),
  },
  template: {
    name: '田字格-标准',
    grid_type: 'tianzige',
    rows: 10,
    cols: 7,
    cell_w_mm: 20,
    cell_h_mm: 20,
    margin_top_mm: 15,
    margin_left_mm: 15,
    line_style: 'solid',
    line_color: '#CCCCCC',
    include_mid_guides: true,
    extra: {
      assist_type: 'none',
      page_size: 'A4',
    },
  },
  font: {
    name: 'KaiTi',
    file_id: 'kaiti.ttf',
    size_pt: 24,
  },
  content: {
    text: '静以修身，俭以养德。',
    repeat_per_char: 2,
  },
};

interface EditorStore extends EditorState {
  // 配置更新方法
  updateConfig: (config: Partial<CopybookConfig>) => void;
  updateTemplate: (template: Partial<CopybookTemplate>) => void;
  updateFont: (font: Partial<CopybookFont>) => void;
  updateContent: (content: Partial<CopybookContent>) => void;
  
  // 页面控制
  setCurrentPage: (page: number) => void;
  setPreviewMode: (isPreview: boolean) => void;
  
  // 字体管理
  setAvailableFonts: (fonts: FontInfo[]) => void;
  addFont: (font: FontInfo) => void;
  removeFont: (fontId: string) => void;
  setSelectedFont: (fontId: string) => void;
  
  // 模板管理
  setTemplates: (templates: TemplateInfo[]) => void;
  selectTemplate: (templateId: string) => void;
  
  // 配置导入导出
  importConfig: (config: CopybookConfig) => void;
  exportConfig: () => CopybookConfig;
  resetConfig: () => void;
  
  // 多页管理
  addPage: (title: string, text: string) => void;
  removePage: (index: number) => void;
  updatePage: (index: number, title: string, text: string) => void;
}

export const useEditorStore = create<EditorStore>()(
  devtools(
    (set, get) => ({
      // 初始状态
      config: defaultConfig,
      currentPage: 0,
      isPreviewMode: false,
      selectedFont: 'kaiti.ttf',
      availableFonts: [
        {
          id: 'kaiti.ttf',
          name: '楷体',
          filename: 'kaiti.ttf',
          size: 0,
          uploaded_at: new Date().toISOString(),
        },
      ],
      templates: [
        {
          id: 'tianzige',
          name: '田字格',
          description: '标准田字格，适合汉字练习',
          config: {
            grid_type: 'tianzige',
            rows: 10,
            cols: 7,
          },
        },
        {
          id: 'mizige',
          name: '米字格',
          description: '米字格，带对角线辅助',
          config: {
            grid_type: 'mizige',
            rows: 10,
            cols: 7,
          },
        },
      ],

      // 配置更新方法
      updateConfig: (newConfig) =>
        set((state) => ({
          config: { ...state.config, ...newConfig },
        })),

      updateTemplate: (template) =>
        set((state) => ({
          config: {
            ...state.config,
            template: { ...state.config.template, ...template },
          },
        })),

      updateFont: (font) =>
        set((state) => ({
          config: {
            ...state.config,
            font: { ...state.config.font, ...font },
          },
        })),

      updateContent: (content) =>
        set((state) => ({
          config: {
            ...state.config,
            content: { ...state.config.content, ...content },
          },
        })),

      // 页面控制
      setCurrentPage: (page) => set({ currentPage: page }),
      setPreviewMode: (isPreview) => set({ isPreviewMode: isPreview }),

      // 字体管理
      setAvailableFonts: (fonts) => set({ availableFonts: fonts }),
      
      addFont: (font) =>
        set((state) => ({
          availableFonts: [...state.availableFonts, font],
        })),

      removeFont: (fontId) =>
        set((state) => ({
          availableFonts: state.availableFonts.filter((f) => f.id !== fontId),
        })),

      setSelectedFont: (fontId) =>
        set((state) => {
          const font = state.availableFonts.find((f) => f.id === fontId);
          return {
            selectedFont: fontId,
            config: {
              ...state.config,
              font: {
                ...state.config.font,
                file_id: fontId,
                name: font?.name || fontId,
              },
            },
          };
        }),

      // 模板管理
      setTemplates: (templates) => set({ templates }),
      
      selectTemplate: (templateId) =>
        set((state) => {
          const template = state.templates.find((t) => t.id === templateId);
          if (template?.config) {
            return {
              config: {
                ...state.config,
                template: { ...state.config.template, ...template.config },
              },
            };
          }
          return state;
        }),

      // 配置导入导出
      importConfig: (config) =>
        set({
          config,
          currentPage: 0,
          isPreviewMode: false,
        }),

      exportConfig: () => get().config,

      resetConfig: () =>
        set({
          config: defaultConfig,
          currentPage: 0,
          isPreviewMode: false,
        }),

      // 多页管理
      addPage: (title, text) =>
        set((state) => ({
          config: {
            ...state.config,
            multi_pages: [
              ...(state.config.multi_pages || []),
              { title, text },
            ],
          },
        })),

      removePage: (index) =>
        set((state) => ({
          config: {
            ...state.config,
            multi_pages: state.config.multi_pages?.filter((_, i) => i !== index),
          },
        })),

      updatePage: (index, title, text) =>
        set((state) => ({
          config: {
            ...state.config,
            multi_pages: state.config.multi_pages?.map((page, i) =>
              i === index ? { title, text } : page
            ),
          },
        })),
    }),
    {
      name: 'copybook-editor-store',
    }
  )
);
