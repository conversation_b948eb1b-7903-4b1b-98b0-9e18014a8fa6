import React, { useState } from 'react';
import { useDropzone } from 'react-dropzone';
import { 
  Settings, 
  Type, 
  FileText, 
  Download, 
  Upload,
  Save,
  RotateCcw,
  Plus,
  Minus
} from 'lucide-react';
import { useEditorStore } from '../stores/editorStore';
import { readFileAsJson, exportJsonFile } from '../services/api';
import type { CopybookConfig } from '../types';

/**
 * 编辑器控制面板组件
 * 包含所有编辑功能：模板选择、字体设置、内容编辑、配置导入导出等
 */
const EditorPanel: React.FC = () => {
  const {
    config,
    templates,
    availableFonts,
    updateTemplate,
    updateFont,
    updateContent,
    selectTemplate,
    setSelectedFont,
    importConfig,
    exportConfig,
    resetConfig,
    addPage,
    removePage,
    updatePage,
  } = useEditorStore();

  const [activeTab, setActiveTab] = useState<'template' | 'font' | 'content' | 'pages'>('content');
  const [isImporting, setIsImporting] = useState(false);

  // 配置文件导入
  const { getRootProps, getInputProps } = useDropzone({
    accept: {
      'application/json': ['.json'],
    },
    multiple: false,
    onDrop: async (acceptedFiles) => {
      if (acceptedFiles.length === 0) return;
      
      setIsImporting(true);
      try {
        const file = acceptedFiles[0];
        const configData = await readFileAsJson(file);
        
        // 验证配置文件格式
        if (configData.meta && configData.template && configData.font && configData.content) {
          importConfig(configData as CopybookConfig);
          alert('配置导入成功！');
        } else {
          alert('配置文件格式不正确！');
        }
      } catch (error) {
        console.error('Import failed:', error);
        alert('配置文件导入失败！');
      } finally {
        setIsImporting(false);
      }
    },
  });

  // 导出配置
  const handleExportConfig = () => {
    const currentConfig = exportConfig();
    const filename = `copybook-config-${new Date().toISOString().slice(0, 10)}.json`;
    exportJsonFile(currentConfig, filename);
  };

  // 渲染模板选择标签页
  const renderTemplateTab = () => (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold flex items-center gap-2">
        <Settings className="w-5 h-5" />
        模板设置
      </h3>
      
      {/* 预设模板选择 */}
      <div>
        <label className="block text-sm font-medium mb-2">选择模板</label>
        <div className="grid grid-cols-2 gap-2">
          {templates.map((template) => (
            <button
              key={template.id}
              onClick={() => selectTemplate(template.id)}
              className={`p-3 border rounded-lg text-left transition-colors ${
                config.template.grid_type === template.config.grid_type
                  ? 'border-primary-500 bg-primary-50'
                  : 'border-gray-300 hover:border-gray-400'
              }`}
            >
              <div className="font-medium">{template.name}</div>
              <div className="text-xs text-gray-500">{template.description}</div>
            </button>
          ))}
        </div>
      </div>

      {/* 布局参数 */}
      <div className="grid grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium mb-1">行数</label>
          <input
            type="number"
            min="1"
            max="20"
            value={config.template.rows}
            onChange={(e) => updateTemplate({ rows: parseInt(e.target.value) })}
            className="input-field w-full"
          />
        </div>
        <div>
          <label className="block text-sm font-medium mb-1">列数</label>
          <input
            type="number"
            min="1"
            max="20"
            value={config.template.cols}
            onChange={(e) => updateTemplate({ cols: parseInt(e.target.value) })}
            className="input-field w-full"
          />
        </div>
      </div>

      {/* 格子尺寸 */}
      <div className="grid grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium mb-1">格子宽度 (mm)</label>
          <input
            type="number"
            min="10"
            max="50"
            value={config.template.cell_w_mm}
            onChange={(e) => updateTemplate({ cell_w_mm: parseInt(e.target.value) })}
            className="input-field w-full"
          />
        </div>
        <div>
          <label className="block text-sm font-medium mb-1">格子高度 (mm)</label>
          <input
            type="number"
            min="10"
            max="50"
            value={config.template.cell_h_mm}
            onChange={(e) => updateTemplate({ cell_h_mm: parseInt(e.target.value) })}
            className="input-field w-full"
          />
        </div>
      </div>

      {/* 边距设置 */}
      <div className="grid grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium mb-1">上边距 (mm)</label>
          <input
            type="number"
            min="5"
            max="50"
            value={config.template.margin_top_mm}
            onChange={(e) => updateTemplate({ margin_top_mm: parseInt(e.target.value) })}
            className="input-field w-full"
          />
        </div>
        <div>
          <label className="block text-sm font-medium mb-1">左边距 (mm)</label>
          <input
            type="number"
            min="5"
            max="50"
            value={config.template.margin_left_mm}
            onChange={(e) => updateTemplate({ margin_left_mm: parseInt(e.target.value) })}
            className="input-field w-full"
          />
        </div>
      </div>

      {/* 线条样式 */}
      <div className="grid grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium mb-1">线条样式</label>
          <select
            value={config.template.line_style}
            onChange={(e) => updateTemplate({ line_style: e.target.value as any })}
            className="input-field w-full"
          >
            <option value="solid">实线</option>
            <option value="dashed">虚线</option>
            <option value="dotted">点线</option>
          </select>
        </div>
        <div>
          <label className="block text-sm font-medium mb-1">线条颜色</label>
          <input
            type="color"
            value={config.template.line_color}
            onChange={(e) => updateTemplate({ line_color: e.target.value })}
            className="input-field w-full h-10"
          />
        </div>
      </div>

      {/* 页面尺寸 */}
      <div>
        <label className="block text-sm font-medium mb-1">页面尺寸</label>
        <select
          value={config.template.extra?.page_size || 'A4'}
          onChange={(e) => updateTemplate({ 
            extra: { ...config.template.extra, page_size: e.target.value as 'A4' | 'A3' }
          })}
          className="input-field w-full"
        >
          <option value="A4">A4 (210×297mm)</option>
          <option value="A3">A3 (297×420mm)</option>
        </select>
      </div>
    </div>
  );

  // 渲染字体设置标签页
  const renderFontTab = () => (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold flex items-center gap-2">
        <Type className="w-5 h-5" />
        字体设置
      </h3>
      
      {/* 字体选择 */}
      <div>
        <label className="block text-sm font-medium mb-2">选择字体</label>
        <select
          value={config.font.file_id}
          onChange={(e) => setSelectedFont(e.target.value)}
          className="input-field w-full"
        >
          {availableFonts.map((font) => (
            <option key={font.id} value={font.id}>
              {font.name}
            </option>
          ))}
        </select>
      </div>

      {/* 字体大小 */}
      <div>
        <label className="block text-sm font-medium mb-1">字体大小 (pt)</label>
        <input
          type="number"
          min="8"
          max="72"
          value={config.font.size_pt}
          onChange={(e) => updateFont({ size_pt: parseInt(e.target.value) })}
          className="input-field w-full"
        />
      </div>

      {/* 字体上传 */}
      <div>
        <label className="block text-sm font-medium mb-2">上传自定义字体</label>
        <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center">
          <input
            type="file"
            accept=".ttf,.otf,.woff,.woff2"
            className="hidden"
            id="font-upload"
          />
          <label htmlFor="font-upload" className="cursor-pointer">
            <Upload className="w-8 h-8 mx-auto mb-2 text-gray-400" />
            <p className="text-sm text-gray-600">
              点击上传字体文件 (.ttf, .otf)
            </p>
          </label>
        </div>
      </div>
    </div>
  );

  // 渲染内容编辑标签页
  const renderContentTab = () => (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold flex items-center gap-2">
        <FileText className="w-5 h-5" />
        内容编辑
      </h3>
      
      {/* 练习内容 */}
      <div>
        <label className="block text-sm font-medium mb-1">练习内容</label>
        <textarea
          value={config.content.text}
          onChange={(e) => updateContent({ text: e.target.value })}
          placeholder="请输入要练习的文字内容..."
          className="input-field w-full h-32 resize-none"
        />
      </div>

      {/* 重复次数 */}
      <div>
        <label className="block text-sm font-medium mb-1">每字重复次数</label>
        <input
          type="number"
          min="1"
          max="10"
          value={config.content.repeat_per_char}
          onChange={(e) => updateContent({ repeat_per_char: parseInt(e.target.value) })}
          className="input-field w-full"
        />
      </div>

      {/* 拼音标注 */}
      {config.template.grid_type === 'pinyin' && (
        <div>
          <label className="block text-sm font-medium mb-1">拼音标注</label>
          <textarea
            value={config.content.pinyin?.join(' ') || ''}
            onChange={(e) => updateContent({ 
              pinyin: e.target.value.split(' ').filter(p => p.trim()) 
            })}
            placeholder="请输入对应的拼音，用空格分隔..."
            className="input-field w-full h-20 resize-none"
          />
          <p className="text-xs text-gray-500 mt-1">
            拼音数量应与文字数量对应
          </p>
        </div>
      )}
    </div>
  );

  // 渲染多页管理标签页
  const renderPagesTab = () => (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold flex items-center gap-2">
        <FileText className="w-5 h-5" />
        多页管理
      </h3>
      
      {/* 添加页面 */}
      <button
        onClick={() => addPage('新页面', '')}
        className="btn-primary w-full flex items-center justify-center gap-2"
      >
        <Plus className="w-4 h-4" />
        添加新页面
      </button>

      {/* 页面列表 */}
      {config.multi_pages?.map((page, index) => (
        <div key={index} className="card">
          <div className="flex items-center justify-between mb-2">
            <input
              type="text"
              value={page.title}
              onChange={(e) => updatePage(index, e.target.value, page.text)}
              className="input-field flex-1 mr-2"
              placeholder="页面标题"
            />
            <button
              onClick={() => removePage(index)}
              className="text-red-500 hover:text-red-700"
            >
              <Minus className="w-4 h-4" />
            </button>
          </div>
          <textarea
            value={page.text}
            onChange={(e) => updatePage(index, page.title, e.target.value)}
            className="input-field w-full h-20 resize-none"
            placeholder="页面内容"
          />
        </div>
      ))}
    </div>
  );

  return (
    <div className="editor-panel bg-white border-r border-gray-200 w-80 h-screen overflow-y-auto">
      <div className="p-4">
        {/* 标题和操作按钮 */}
        <div className="mb-6">
          <h2 className="text-xl font-bold mb-4">字帖编辑器</h2>
          
          {/* 配置导入导出 */}
          <div className="flex gap-2 mb-4">
            <div {...getRootProps()} className="flex-1">
              <input {...getInputProps()} />
              <button
                className="btn-secondary w-full flex items-center justify-center gap-2"
                disabled={isImporting}
              >
                <Upload className="w-4 h-4" />
                {isImporting ? '导入中...' : '导入配置'}
              </button>
            </div>
            
            <button
              onClick={handleExportConfig}
              className="btn-secondary flex items-center gap-2"
            >
              <Download className="w-4 h-4" />
              导出
            </button>
            
            <button
              onClick={resetConfig}
              className="btn-secondary flex items-center gap-2"
              title="重置配置"
            >
              <RotateCcw className="w-4 h-4" />
            </button>
          </div>
        </div>

        {/* 标签页导航 */}
        <div className="flex border-b border-gray-200 mb-4">
          {[
            { key: 'content', label: '内容' },
            { key: 'template', label: '模板' },
            { key: 'font', label: '字体' },
            { key: 'pages', label: '多页' },
          ].map((tab) => (
            <button
              key={tab.key}
              onClick={() => setActiveTab(tab.key as any)}
              className={`px-3 py-2 text-sm font-medium border-b-2 transition-colors ${
                activeTab === tab.key
                  ? 'border-primary-500 text-primary-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700'
              }`}
            >
              {tab.label}
            </button>
          ))}
        </div>

        {/* 标签页内容 */}
        <div className="tab-content">
          {activeTab === 'template' && renderTemplateTab()}
          {activeTab === 'font' && renderFontTab()}
          {activeTab === 'content' && renderContentTab()}
          {activeTab === 'pages' && renderPagesTab()}
        </div>
      </div>
    </div>
  );
};

export default EditorPanel;
