from typing import List, Optional
from fastapi import HTTPException
from ..models import CopybookConfig, ConfigInfo, create_success_response, create_error_response
from ..storage.file_storage import FileStorage

class ConfigService:
    """配置管理服务"""
    
    def __init__(self, storage: FileStorage):
        self.storage = storage
    
    def save_config(self, config_data: dict) -> dict:
        """保存配置"""
        try:
            # 验证配置数据
            config = CopybookConfig(**config_data)
            
            # 保存到存储
            config_id = self.storage.save_config(config.dict())
            
            return create_success_response(
                data={"id": config_id},
                message="配置保存成功"
            )
            
        except ValueError as e:
            return create_error_response(f"配置数据格式错误: {str(e)}")
        except Exception as e:
            return create_error_response(f"保存配置失败: {str(e)}")
    
    def get_configs(self) -> dict:
        """获取所有配置列表"""
        try:
            configs_data = self.storage.get_configs()
            configs = []
            
            for config_data in configs_data:
                config_info = ConfigInfo(
                    id=config_data["id"],
                    name=config_data["name"],
                    created_at=config_data["created_at"]
                )
                configs.append(config_info)
            
            return create_success_response(
                data=[config.dict() for config in configs],
                message="获取配置列表成功"
            )
            
        except Exception as e:
            return create_error_response(f"获取配置列表失败: {str(e)}")
    
    def get_config(self, config_id: str) -> dict:
        """获取指定配置"""
        try:
            config_data = self.storage.get_config(config_id)
            
            if config_data is None:
                return create_error_response("配置不存在")
            
            # 验证配置数据
            config = CopybookConfig(**config_data)
            
            return create_success_response(
                data=config.dict(),
                message="获取配置成功"
            )
            
        except ValueError as e:
            return create_error_response(f"配置数据格式错误: {str(e)}")
        except Exception as e:
            return create_error_response(f"获取配置失败: {str(e)}")
    
    def delete_config(self, config_id: str) -> dict:
        """删除配置"""
        try:
            success = self.storage.delete_config(config_id)
            
            if success:
                return create_success_response(message="配置删除成功")
            else:
                return create_error_response("配置不存在")
                
        except Exception as e:
            return create_error_response(f"删除配置失败: {str(e)}")
    
    def validate_config(self, config_data: dict) -> dict:
        """验证配置数据"""
        try:
            # 尝试解析配置
            config = CopybookConfig(**config_data)
            
            return create_success_response(
                data=config.dict(),
                message="配置验证成功"
            )
            
        except ValueError as e:
            return create_error_response(f"配置验证失败: {str(e)}")
        except Exception as e:
            return create_error_response(f"配置验证错误: {str(e)}")
