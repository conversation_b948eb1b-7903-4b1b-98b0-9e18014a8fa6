import type { 
  ApiResponse, 
  FontInfo, 
  TemplateInfo, 
  UploadResponse,
  CopybookConfig 
} from '../types';

const API_BASE_URL = 'http://localhost:8000/api';

class ApiService {
  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    try {
      const response = await fetch(`${API_BASE_URL}${endpoint}`, {
        headers: {
          'Content-Type': 'application/json',
          ...options.headers,
        },
        ...options,
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error('API request failed:', error);
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  // 字体管理 API
  async getFonts(): Promise<ApiResponse<FontInfo[]>> {
    return this.request<FontInfo[]>('/fonts');
  }

  async uploadFont(file: File): Promise<ApiResponse<UploadResponse>> {
    const formData = new FormData();
    formData.append('file', file);

    try {
      const response = await fetch(`${API_BASE_URL}/fonts/upload`, {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error('Font upload failed:', error);
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Upload failed',
      };
    }
  }

  async deleteFont(fontId: string): Promise<ApiResponse<void>> {
    return this.request<void>(`/fonts/${fontId}`, {
      method: 'DELETE',
    });
  }

  async downloadFont(fontId: string): Promise<string> {
    return `${API_BASE_URL}/fonts/${fontId}/download`;
  }

  // 模板管理 API
  async getTemplates(): Promise<ApiResponse<TemplateInfo[]>> {
    return this.request<TemplateInfo[]>('/templates');
  }

  async saveTemplate(template: TemplateInfo): Promise<ApiResponse<TemplateInfo>> {
    return this.request<TemplateInfo>('/templates', {
      method: 'POST',
      body: JSON.stringify(template),
    });
  }

  async updateTemplate(
    templateId: string,
    template: Partial<TemplateInfo>
  ): Promise<ApiResponse<TemplateInfo>> {
    return this.request<TemplateInfo>(`/templates/${templateId}`, {
      method: 'PUT',
      body: JSON.stringify(template),
    });
  }

  async deleteTemplate(templateId: string): Promise<ApiResponse<void>> {
    return this.request<void>(`/templates/${templateId}`, {
      method: 'DELETE',
    });
  }

  // 配置管理 API
  async saveConfig(config: CopybookConfig): Promise<ApiResponse<{ id: string }>> {
    return this.request<{ id: string }>('/configs', {
      method: 'POST',
      body: JSON.stringify(config),
    });
  }

  async getConfig(configId: string): Promise<ApiResponse<CopybookConfig>> {
    return this.request<CopybookConfig>(`/configs/${configId}`);
  }

  async getConfigs(): Promise<ApiResponse<{ id: string; name: string; created_at: string }[]>> {
    return this.request<{ id: string; name: string; created_at: string }[]>('/configs');
  }

  async deleteConfig(configId: string): Promise<ApiResponse<void>> {
    return this.request<void>(`/configs/${configId}`, {
      method: 'DELETE',
    });
  }
}

export const apiService = new ApiService();

// 工具函数：处理文件上传
export const uploadFile = async (
  file: File,
  onProgress?: (progress: number) => void
): Promise<ApiResponse<UploadResponse>> => {
  return new Promise((resolve) => {
    const formData = new FormData();
    formData.append('file', file);

    const xhr = new XMLHttpRequest();

    xhr.upload.addEventListener('progress', (event) => {
      if (event.lengthComputable && onProgress) {
        const progress = (event.loaded / event.total) * 100;
        onProgress(progress);
      }
    });

    xhr.addEventListener('load', () => {
      if (xhr.status === 200) {
        try {
          const response = JSON.parse(xhr.responseText);
          resolve(response);
        } catch (error) {
          resolve({
            success: false,
            message: 'Invalid response format',
          });
        }
      } else {
        resolve({
          success: false,
          message: `Upload failed with status: ${xhr.status}`,
        });
      }
    });

    xhr.addEventListener('error', () => {
      resolve({
        success: false,
        message: 'Network error during upload',
      });
    });

    xhr.open('POST', `${API_BASE_URL}/fonts/upload`);
    xhr.send(formData);
  });
};

// 工具函数：下载文件
export const downloadFile = (url: string, filename: string): void => {
  const link = document.createElement('a');
  link.href = url;
  link.download = filename;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};

// 工具函数：读取本地文件为 JSON
export const readFileAsJson = (file: File): Promise<any> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    
    reader.onload = (event) => {
      try {
        const json = JSON.parse(event.target?.result as string);
        resolve(json);
      } catch (error) {
        reject(new Error('Invalid JSON file'));
      }
    };
    
    reader.onerror = () => {
      reject(new Error('Failed to read file'));
    };
    
    reader.readAsText(file);
  });
};

// 工具函数：导出 JSON 文件
export const exportJsonFile = (data: any, filename: string): void => {
  const json = JSON.stringify(data, null, 2);
  const blob = new Blob([json], { type: 'application/json' });
  const url = URL.createObjectURL(blob);
  
  downloadFile(url, filename);
  
  // 清理 URL 对象
  setTimeout(() => URL.revokeObjectURL(url), 100);
};
