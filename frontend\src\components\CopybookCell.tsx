import React from 'react';
import type { CopybookCellProps } from '../types';

/**
 * 字帖单元格组件
 * 支持不同类型的格子样式：田字格、米字格、九宫格等
 */
const CopybookCell: React.FC<CopybookCellProps> = ({
  char,
  pinyin,
  gridType,
  cellSize,
  fontSize,
  fontFamily,
  lineColor,
  lineStyle,
}) => {
  const cellStyle: React.CSSProperties = {
    width: `${cellSize.width}px`,
    height: `${cellSize.height}px`,
    border: `1px ${lineStyle} ${lineColor}`,
    position: 'relative',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    fontFamily,
    fontSize: `${fontSize}px`,
    color: '#333',
    backgroundColor: 'white',
  };

  const renderGridLines = () => {
    const lineStyles: React.CSSProperties = {
      position: 'absolute',
      borderColor: lineColor,
      borderStyle: lineStyle,
    };

    switch (gridType) {
      case 'tianzige':
        return (
          <>
            {/* 垂直中线 */}
            <div
              style={{
                ...lineStyles,
                left: '50%',
                top: 0,
                bottom: 0,
                borderLeftWidth: '1px',
                transform: 'translateX(-50%)',
              }}
            />
            {/* 水平中线 */}
            <div
              style={{
                ...lineStyles,
                top: '50%',
                left: 0,
                right: 0,
                borderTopWidth: '1px',
                transform: 'translateY(-50%)',
              }}
            />
          </>
        );

      case 'mizige':
        return (
          <>
            {/* 田字格基础线 */}
            <div
              style={{
                ...lineStyles,
                left: '50%',
                top: 0,
                bottom: 0,
                borderLeftWidth: '1px',
                transform: 'translateX(-50%)',
              }}
            />
            <div
              style={{
                ...lineStyles,
                top: '50%',
                left: 0,
                right: 0,
                borderTopWidth: '1px',
                transform: 'translateY(-50%)',
              }}
            />
            {/* 对角线 */}
            <div
              style={{
                ...lineStyles,
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
                borderTopWidth: '1px',
                transform: 'rotate(45deg)',
                transformOrigin: 'center',
              }}
            />
            <div
              style={{
                ...lineStyles,
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
                borderTopWidth: '1px',
                transform: 'rotate(-45deg)',
                transformOrigin: 'center',
              }}
            />
          </>
        );

      case 'jiugongge':
        return (
          <>
            {/* 垂直三等分线 */}
            <div
              style={{
                ...lineStyles,
                left: '33.33%',
                top: 0,
                bottom: 0,
                borderLeftWidth: '1px',
                transform: 'translateX(-50%)',
              }}
            />
            <div
              style={{
                ...lineStyles,
                left: '66.67%',
                top: 0,
                bottom: 0,
                borderLeftWidth: '1px',
                transform: 'translateX(-50%)',
              }}
            />
            {/* 水平三等分线 */}
            <div
              style={{
                ...lineStyles,
                top: '33.33%',
                left: 0,
                right: 0,
                borderTopWidth: '1px',
                transform: 'translateY(-50%)',
              }}
            />
            <div
              style={{
                ...lineStyles,
                top: '66.67%',
                left: 0,
                right: 0,
                borderTopWidth: '1px',
                transform: 'translateY(-50%)',
              }}
            />
          </>
        );

      case 'pinyin':
        return (
          <>
            {/* 拼音四线三格 */}
            <div
              style={{
                ...lineStyles,
                top: '25%',
                left: 0,
                right: 0,
                borderTopWidth: '1px',
              }}
            />
            <div
              style={{
                ...lineStyles,
                top: '50%',
                left: 0,
                right: 0,
                borderTopWidth: '2px', // 基线加粗
              }}
            />
            <div
              style={{
                ...lineStyles,
                top: '75%',
                left: 0,
                right: 0,
                borderTopWidth: '1px',
              }}
            />
          </>
        );

      case 'english':
        return (
          <>
            {/* 英文双线 */}
            <div
              style={{
                ...lineStyles,
                top: '40%',
                left: 0,
                right: 0,
                borderTopWidth: '1px',
              }}
            />
            <div
              style={{
                ...lineStyles,
                top: '60%',
                left: 0,
                right: 0,
                borderTopWidth: '1px',
              }}
            />
          </>
        );

      default:
        return null;
    }
  };

  const renderContent = () => {
    if (gridType === 'pinyin' && pinyin) {
      return (
        <div className="w-full h-full flex flex-col">
          {/* 拼音区域 */}
          <div 
            className="flex-1 flex items-center justify-center text-xs"
            style={{ fontSize: `${fontSize * 0.4}px` }}
          >
            {pinyin}
          </div>
          {/* 汉字区域 */}
          <div 
            className="flex-1 flex items-center justify-center"
            style={{ fontSize: `${fontSize}px` }}
          >
            {char}
          </div>
        </div>
      );
    }

    return (
      <div className="flex items-center justify-center w-full h-full">
        {char}
      </div>
    );
  };

  return (
    <div style={cellStyle} className="copybook-cell">
      {renderGridLines()}
      <div className="relative z-10">
        {renderContent()}
      </div>
    </div>
  );
};

export default CopybookCell;
