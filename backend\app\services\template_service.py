from typing import List, Optional
from fastapi import HTTPException
from ..models import TemplateInfo, CopybookTemplate, create_success_response, create_error_response
from ..storage.file_storage import FileStorage

class TemplateService:
    """模板管理服务"""
    
    def __init__(self, storage: FileStorage):
        self.storage = storage
    
    def get_templates(self) -> dict:
        """获取所有模板"""
        try:
            templates_data = self.storage.get_templates()
            templates = []
            
            for template_data in templates_data:
                template = TemplateInfo(**template_data)
                templates.append(template)
            
            return create_success_response(
                data=[template.dict() for template in templates],
                message="获取模板列表成功"
            )
            
        except Exception as e:
            return create_error_response(f"获取模板列表失败: {str(e)}")
    
    def save_template(self, template_data: dict) -> dict:
        """保存模板"""
        try:
            # 验证模板数据
            template = TemplateInfo(**template_data)
            
            # 保存到存储
            saved_template = self.storage.save_template(template.dict())
            
            return create_success_response(
                data=saved_template,
                message="模板保存成功"
            )
            
        except ValueError as e:
            return create_error_response(f"模板数据格式错误: {str(e)}")
        except Exception as e:
            return create_error_response(f"保存模板失败: {str(e)}")
    
    def update_template(self, template_id: str, template_data: dict) -> dict:
        """更新模板"""
        try:
            # 确保 ID 一致
            template_data["id"] = template_id
            
            # 验证模板数据
            template = TemplateInfo(**template_data)
            
            # 保存到存储
            saved_template = self.storage.save_template(template.dict())
            
            return create_success_response(
                data=saved_template,
                message="模板更新成功"
            )
            
        except ValueError as e:
            return create_error_response(f"模板数据格式错误: {str(e)}")
        except Exception as e:
            return create_error_response(f"更新模板失败: {str(e)}")
    
    def delete_template(self, template_id: str) -> dict:
        """删除模板"""
        try:
            success = self.storage.delete_template(template_id)
            
            if success:
                return create_success_response(message="模板删除成功")
            else:
                return create_error_response("模板不存在")
                
        except Exception as e:
            return create_error_response(f"删除模板失败: {str(e)}")
    
    def get_template(self, template_id: str) -> Optional[TemplateInfo]:
        """获取指定模板"""
        templates = self.storage.get_templates()
        for template_data in templates:
            if template_data["id"] == template_id:
                return TemplateInfo(**template_data)
        return None
