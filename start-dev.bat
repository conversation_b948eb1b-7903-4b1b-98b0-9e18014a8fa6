@echo off
echo 启动字帖生成器开发环境...

echo.
echo 启动后端服务器...
start "Backend Server" cmd /k "cd backend && python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000"

echo.
echo 等待后端服务器启动...
timeout /t 3 /nobreak > nul

echo.
echo 启动前端开发服务器...
start "Frontend Server" cmd /k "cd frontend && npm run dev"

echo.
echo 等待前端服务器启动...
timeout /t 5 /nobreak > nul

echo.
echo 开发环境启动完成！
echo 前端地址: http://localhost:5173
echo 后端地址: http://localhost:8000
echo API 文档: http://localhost:8000/docs
echo.
echo 按任意键退出...
pause > nul
