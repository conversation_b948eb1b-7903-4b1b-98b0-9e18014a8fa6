/* 基础样式重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f9fafb;
  color: #111827;
}

/* 字帖格子样式 */
.copybook-cell {
  border: 1px solid #d1d5db;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: white;
}

.copybook-cell.tianzige {
  border: 2px solid #d1d5db;
}

.copybook-cell.tianzige::before {
  content: '';
  position: absolute;
  left: 50%;
  top: 0;
  bottom: 0;
  width: 0;
  border-left: 1px solid #e5e7eb;
}

.copybook-cell.tianzige::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 0;
  border-top: 1px solid #e5e7eb;
}

/* 按钮样式 */
.btn-primary {
  background-color: #3b82f6;
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  border: none;
  cursor: pointer;
  transition: background-color 0.2s;
}

.btn-primary:hover {
  background-color: #2563eb;
}

.btn-primary:disabled {
  background-color: #9ca3af;
  cursor: not-allowed;
}

.btn-secondary {
  background-color: #e5e7eb;
  color: #374151;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  border: none;
  cursor: pointer;
  transition: background-color 0.2s;
}

.btn-secondary:hover {
  background-color: #d1d5db;
}

/* 输入框样式 */
.input-field {
  border: 1px solid #d1d5db;
  border-radius: 0.5rem;
  padding: 0.5rem 0.75rem;
  outline: none;
  transition: border-color 0.2s, box-shadow 0.2s;
}

.input-field:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* 卡片样式 */
.card {
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
  padding: 1rem;
}

/* 布局样式 */
.app {
  min-height: 100vh;
  background-color: #f9fafb;
  display: flex;
}

.flex {
  display: flex;
}

.flex-1 {
  flex: 1;
}

.flex-col {
  flex-direction: column;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.gap-2 {
  gap: 0.5rem;
}

.gap-3 {
  gap: 0.75rem;
}

.gap-4 {
  gap: 1rem;
}

.w-full {
  width: 100%;
}

.h-full {
  height: 100%;
}

.h-screen {
  height: 100vh;
}

.w-80 {
  width: 20rem;
}

.p-4 {
  padding: 1rem;
}

.px-6 {
  padding-left: 1.5rem;
  padding-right: 1.5rem;
}

.py-4 {
  padding-top: 1rem;
  padding-bottom: 1rem;
}

.mb-4 {
  margin-bottom: 1rem;
}

.mb-6 {
  margin-bottom: 1.5rem;
}

.text-xl {
  font-size: 1.25rem;
}

.text-2xl {
  font-size: 1.5rem;
}

.font-bold {
  font-weight: 700;
}

.font-semibold {
  font-weight: 600;
}

.text-gray-900 {
  color: #111827;
}

.text-gray-600 {
  color: #4b5563;
}

.text-gray-500 {
  color: #6b7280;
}

.bg-white {
  background-color: white;
}

.border-b {
  border-bottom-width: 1px;
}

.border-gray-200 {
  border-color: #e5e7eb;
}

.rounded-lg {
  border-radius: 0.5rem;
}

.overflow-auto {
  overflow: auto;
}

.overflow-y-auto {
  overflow-y: auto;
}

.relative {
  position: relative;
}

.absolute {
  position: absolute;
}

.z-10 {
  z-index: 10;
}

.grid {
  display: grid;
}

.grid-cols-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}

.space-y-4 > * + * {
  margin-top: 1rem;
}

.text-center {
  text-align: center;
}

.text-sm {
  font-size: 0.875rem;
}

.text-xs {
  font-size: 0.75rem;
}

.min-h-screen {
  min-height: 100vh;
}

.shadow-sm {
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

.transition-colors {
  transition-property: color, background-color, border-color;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.cursor-pointer {
  cursor: pointer;
}

.cursor-not-allowed {
  cursor: not-allowed;
}

.resize-none {
  resize: none;
}

.h-32 {
  height: 8rem;
}

.h-20 {
  height: 5rem;
}

.h-10 {
  height: 2.5rem;
}

.mr-2 {
  margin-right: 0.5rem;
}

.mt-1 {
  margin-top: 0.25rem;
}

.mt-4 {
  margin-top: 1rem;
}

.block {
  display: block;
}

.hidden {
  display: none;
}

.font-medium {
  font-weight: 500;
}

.border-2 {
  border-width: 2px;
}

.border-dashed {
  border-style: dashed;
}

.border-gray-300 {
  border-color: #d1d5db;
}

.text-gray-400 {
  color: #9ca3af;
}

.w-8 {
  width: 2rem;
}

.h-8 {
  height: 2rem;
}

.w-4 {
  width: 1rem;
}

.h-4 {
  height: 1rem;
}

.w-5 {
  width: 1.25rem;
}

.h-5 {
  height: 1.25rem;
}

.mx-auto {
  margin-left: auto;
  margin-right: auto;
}

.p-1 {
  padding: 0.25rem;
}

.p-3 {
  padding: 0.75rem;
}

.p-8 {
  padding: 2rem;
}

.text-left {
  text-align: left;
}

.border {
  border-width: 1px;
}

.rounded {
  border-radius: 0.25rem;
}

.hover\:border-gray-400:hover {
  border-color: #9ca3af;
}

.hover\:text-gray-900:hover {
  color: #111827;
}

.hover\:text-gray-700:hover {
  color: #374151;
}

.text-red-500 {
  color: #ef4444;
}

.hover\:text-red-700:hover {
  color: #b91c1c;
}

.bg-gray-100 {
  background-color: #f3f4f6;
}

.bg-primary-100 {
  background-color: #dbeafe;
}

.text-primary-700 {
  color: #1d4ed8;
}

.border-primary-500 {
  border-color: #3b82f6;
}

.bg-primary-50 {
  background-color: #eff6ff;
}

.border-transparent {
  border-color: transparent;
}

.text-gray-300 {
  color: #d1d5db;
}

.text-gray-700 {
  color: #374151;
}

.text-gray-800 {
  color: #1f2937;
}

.bg-gray-50 {
  background-color: #f9fafb;
}

.copybook-preview {
  background-color: #f3f4f6;
  padding: 2rem;
  min-height: 100vh;
}

.copybook-page {
  background-color: white;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  position: relative;
  margin: 0 auto;
}

.editor-panel {
  background-color: white;
  border-right: 1px solid #e5e7eb;
  width: 20rem;
  height: 100vh;
  overflow-y: auto;
}

.grid-container {
  display: block;
}

@media print {
  .app {
    display: block !important;
  }

  header,
  .editor-panel {
    display: none !important;
  }

  .copybook-preview {
    padding: 0 !important;
    background: white !important;
  }

  .copybook-page {
    box-shadow: none !important;
    margin: 0 !important;
  }
}
