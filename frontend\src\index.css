@tailwind base;
@tailwind components;
@tailwind utilities;

/* 自定义样式 */
@layer base {
  body {
    @apply bg-gray-50 text-gray-900;
  }
}

@layer components {
  /* 字帖格子样式 */
  .copybook-cell {
    @apply border border-gray-300 relative;
  }

  .copybook-cell.tianzige {
    @apply border-2;
  }

  .copybook-cell.tianzige::before {
    content: '';
    @apply absolute inset-0 border border-gray-200;
    left: 50%;
    top: 0;
    bottom: 0;
    width: 0;
  }

  .copybook-cell.tianzige::after {
    content: '';
    @apply absolute inset-0 border border-gray-200;
    top: 50%;
    left: 0;
    right: 0;
    height: 0;
  }

  /* 米字格样式 */
  .copybook-cell.mizige::before,
  .copybook-cell.mizige::after {
    content: '';
    @apply absolute border border-gray-200;
  }

  .copybook-cell.mizige::before {
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-width: 0 0 1px 1px;
    transform: rotate(45deg);
    transform-origin: center;
  }

  /* 按钮样式 */
  .btn-primary {
    @apply bg-primary-500 hover:bg-primary-600 text-white px-4 py-2 rounded-lg transition-colors;
  }

  .btn-secondary {
    @apply bg-gray-200 hover:bg-gray-300 text-gray-700 px-4 py-2 rounded-lg transition-colors;
  }

  /* 输入框样式 */
  .input-field {
    @apply border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent;
  }

  /* 卡片样式 */
  .card {
    @apply bg-white rounded-lg shadow-sm border border-gray-200 p-4;
  }
}
