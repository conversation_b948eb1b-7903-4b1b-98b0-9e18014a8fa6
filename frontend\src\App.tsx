import React, { useState, useEffect } from 'react';
import { Eye, Edit, FileDown, Printer, ChevronLeft, ChevronRight } from 'lucide-react';
import EditorPanel from './components/EditorPanel';
import CopybookPreview from './components/CopybookPreview';
import { useEditorStore } from './stores/editorStore';
import { exportJsonFile } from './services/api';
import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';

/**
 * 字帖生成器主应用组件
 */
function App() {
  const {
    config,
    currentPage,
    isPreviewMode,
    setCurrentPage,
    setPreviewMode,
    exportConfig
  } = useEditorStore();

  const [isExporting, setIsExporting] = useState(false);

  // 导出 PDF
  const handleExportPDF = async () => {
    setIsExporting(true);
    try {
      const previewElement = document.querySelector('.copybook-page') as HTMLElement;
      if (!previewElement) {
        alert('预览区域未找到');
        return;
      }

      // 使用 html2canvas 截图
      const canvas = await html2canvas(previewElement, {
        scale: 2, // 提高清晰度
        useCORS: true,
        backgroundColor: '#ffffff',
      });

      // 创建 PDF
      const pdf = new jsPDF({
        orientation: 'portrait',
        unit: 'mm',
        format: config.template.extra?.page_size || 'a4',
      });

      const imgData = canvas.toDataURL('image/png');
      const pageWidth = config.template.extra?.page_size === 'A3' ? 297 : 210;
      const pageHeight = config.template.extra?.page_size === 'A3' ? 420 : 297;

      pdf.addImage(imgData, 'PNG', 0, 0, pageWidth, pageHeight);

      // 如果有多页，添加其他页面
      if (config.multi_pages && config.multi_pages.length > 1) {
        for (let i = 1; i < config.multi_pages.length; i++) {
          setCurrentPage(i);
          // 等待页面更新
          await new Promise(resolve => setTimeout(resolve, 500));

          const pageElement = document.querySelector('.copybook-page') as HTMLElement;
          const pageCanvas = await html2canvas(pageElement, {
            scale: 2,
            useCORS: true,
            backgroundColor: '#ffffff',
          });

          const pageImgData = pageCanvas.toDataURL('image/png');
          pdf.addPage();
          pdf.addImage(pageImgData, 'PNG', 0, 0, pageWidth, pageHeight);
        }
        setCurrentPage(0); // 重置到第一页
      }

      // 保存 PDF
      const filename = `字帖-${new Date().toISOString().slice(0, 10)}.pdf`;
      pdf.save(filename);

      alert('PDF 导出成功！');
    } catch (error) {
      console.error('PDF export failed:', error);
      alert('PDF 导出失败！');
    } finally {
      setIsExporting(false);
    }
  };

  // 打印功能
  const handlePrint = () => {
    window.print();
  };

  // 页面导航
  const totalPages = config.multi_pages?.length || 1;
  const canGoPrev = currentPage > 0;
  const canGoNext = currentPage < totalPages - 1;

  const goToPrevPage = () => {
    if (canGoPrev) {
      setCurrentPage(currentPage - 1);
    }
  };

  const goToNextPage = () => {
    if (canGoNext) {
      setCurrentPage(currentPage + 1);
    }
  };

  return (
    <div className="app min-h-screen bg-gray-50 flex">
      {/* 编辑器面板 */}
      {!isPreviewMode && <EditorPanel />}

      {/* 主内容区域 */}
      <div className="flex-1 flex flex-col">
        {/* 顶部工具栏 */}
        <header className="bg-white border-b border-gray-200 px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <h1 className="text-2xl font-bold text-gray-900">
                字帖生成器
              </h1>

              {/* 页面导航 */}
              {totalPages > 1 && (
                <div className="flex items-center gap-2">
                  <button
                    onClick={goToPrevPage}
                    disabled={!canGoPrev}
                    className={`p-1 rounded ${
                      canGoPrev
                        ? 'text-gray-600 hover:text-gray-900'
                        : 'text-gray-300 cursor-not-allowed'
                    }`}
                  >
                    <ChevronLeft className="w-5 h-5" />
                  </button>

                  <span className="text-sm text-gray-600">
                    {currentPage + 1} / {totalPages}
                  </span>

                  <button
                    onClick={goToNextPage}
                    disabled={!canGoNext}
                    className={`p-1 rounded ${
                      canGoNext
                        ? 'text-gray-600 hover:text-gray-900'
                        : 'text-gray-300 cursor-not-allowed'
                    }`}
                  >
                    <ChevronRight className="w-5 h-5" />
                  </button>
                </div>
              )}
            </div>

            {/* 操作按钮 */}
            <div className="flex items-center gap-3">
              {/* 预览模式切换 */}
              <button
                onClick={() => setPreviewMode(!isPreviewMode)}
                className={`btn-secondary flex items-center gap-2 ${
                  isPreviewMode ? 'bg-primary-100 text-primary-700' : ''
                }`}
              >
                {isPreviewMode ? (
                  <>
                    <Edit className="w-4 h-4" />
                    编辑模式
                  </>
                ) : (
                  <>
                    <Eye className="w-4 h-4" />
                    预览模式
                  </>
                )}
              </button>

              {/* 导出配置 */}
              <button
                onClick={() => {
                  const config = exportConfig();
                  const filename = `字帖配置-${new Date().toISOString().slice(0, 10)}.json`;
                  exportJsonFile(config, filename);
                }}
                className="btn-secondary flex items-center gap-2"
                title="导出配置文件"
              >
                <FileDown className="w-4 h-4" />
                导出配置
              </button>

              {/* 导出 PDF */}
              <button
                onClick={handleExportPDF}
                disabled={isExporting}
                className="btn-primary flex items-center gap-2"
              >
                <FileDown className="w-4 h-4" />
                {isExporting ? '导出中...' : '导出PDF'}
              </button>

              {/* 打印 */}
              <button
                onClick={handlePrint}
                className="btn-secondary flex items-center gap-2"
                title="打印字帖"
              >
                <Printer className="w-4 h-4" />
                打印
              </button>
            </div>
          </div>
        </header>

        {/* 预览区域 */}
        <main className="flex-1 overflow-auto">
          <CopybookPreview />
        </main>
      </div>

      {/* 打印样式 */}
      <style jsx>{`
        @media print {
          .app {
            display: block !important;
          }

          header,
          .editor-panel {
            display: none !important;
          }

          .copybook-preview {
            padding: 0 !important;
            background: white !important;
          }

          .copybook-page {
            box-shadow: none !important;
            margin: 0 !important;
          }
        }
      `}</style>
    </div>
  );
}

export default App;
