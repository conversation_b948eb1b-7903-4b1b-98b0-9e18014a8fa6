import os
from typing import List, Optional
from fastapi import UploadFile, HTTPException
from ..models import FontInfo, UploadResponse, create_success_response, create_error_response
from ..storage.file_storage import FileStorage

class FontService:
    """字体管理服务"""
    
    def __init__(self, storage: FileStorage):
        self.storage = storage
        self.allowed_extensions = {'.ttf', '.otf', '.woff', '.woff2'}
        self.max_file_size = 10 * 1024 * 1024  # 10MB
    
    def _validate_font_file(self, file: UploadFile) -> None:
        """验证字体文件"""
        if not file.filename:
            raise HTTPException(status_code=400, detail="文件名不能为空")
        
        # 检查文件扩展名
        file_ext = os.path.splitext(file.filename)[1].lower()
        if file_ext not in self.allowed_extensions:
            raise HTTPException(
                status_code=400, 
                detail=f"不支持的文件格式。支持的格式: {', '.join(self.allowed_extensions)}"
            )
    
    async def upload_font(self, file: UploadFile) -> dict:
        """上传字体文件"""
        try:
            # 验证文件
            self._validate_font_file(file)
            
            # 读取文件内容
            file_content = await file.read()
            file_size = len(file_content)
            
            # 检查文件大小
            if file_size > self.max_file_size:
                raise HTTPException(
                    status_code=400, 
                    detail=f"文件太大。最大允许 {self.max_file_size // (1024*1024)}MB"
                )
            
            # 保存文件
            file_id = await self.storage.save_font_file(file_content, file.filename)
            
            # 保存字体信息
            font_info = {
                "id": file_id,
                "name": os.path.splitext(file.filename)[0],
                "filename": file.filename,
                "size": file_size
            }
            
            saved_font = self.storage.save_font_info(font_info)
            
            return create_success_response(
                data=UploadResponse(
                    file_id=file_id,
                    filename=file.filename,
                    size=file_size
                ).dict(),
                message="字体上传成功"
            )
            
        except HTTPException:
            raise
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"上传失败: {str(e)}")
    
    def get_fonts(self) -> dict:
        """获取所有字体"""
        try:
            fonts_data = self.storage.get_fonts()
            fonts = [FontInfo(**font) for font in fonts_data]
            
            return create_success_response(
                data=[font.dict() for font in fonts],
                message="获取字体列表成功"
            )
            
        except Exception as e:
            return create_error_response(f"获取字体列表失败: {str(e)}")
    
    def get_font_file_path(self, font_id: str) -> Optional[str]:
        """获取字体文件路径"""
        file_path = self.storage.get_font_file_path(font_id)
        return str(file_path) if file_path else None
    
    def delete_font(self, font_id: str) -> dict:
        """删除字体"""
        try:
            # 删除文件
            file_deleted = self.storage.delete_font_file(font_id)
            
            # 删除信息
            info_deleted = self.storage.delete_font_info(font_id)
            
            if file_deleted or info_deleted:
                return create_success_response(message="字体删除成功")
            else:
                return create_error_response("字体不存在")
                
        except Exception as e:
            return create_error_response(f"删除字体失败: {str(e)}")
    
    def get_font_info(self, font_id: str) -> Optional[FontInfo]:
        """获取字体信息"""
        fonts = self.storage.get_fonts()
        for font_data in fonts:
            if font_data["id"] == font_id:
                return FontInfo(**font_data)
        return None
