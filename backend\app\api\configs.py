from fastapi import APIRouter, HTTPException, Depends
from typing import Dict, Any
from ..services.config_service import ConfigService
from ..storage.file_storage import FileStorage

router = APIRouter(prefix="/configs", tags=["configs"])

# 依赖注入
def get_config_service() -> ConfigService:
    storage = FileStorage()
    return ConfigService(storage)

@router.get("/")
async def get_configs(config_service: ConfigService = Depends(get_config_service)):
    """获取所有配置列表"""
    return config_service.get_configs()

@router.post("/")
async def create_config(
    config_data: Dict[str, Any],
    config_service: ConfigService = Depends(get_config_service)
):
    """保存新配置"""
    return config_service.save_config(config_data)

@router.get("/{config_id}")
async def get_config(
    config_id: str,
    config_service: ConfigService = Depends(get_config_service)
):
    """获取指定配置"""
    return config_service.get_config(config_id)

@router.delete("/{config_id}")
async def delete_config(
    config_id: str,
    config_service: ConfigService = Depends(get_config_service)
):
    """删除配置"""
    return config_service.delete_config(config_id)

@router.post("/validate")
async def validate_config(
    config_data: Dict[str, Any],
    config_service: ConfigService = Depends(get_config_service)
):
    """验证配置数据"""
    return config_service.validate_config(config_data)
