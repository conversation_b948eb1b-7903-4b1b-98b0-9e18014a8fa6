// 字帖配置类型定义

export interface CopybookMeta {
  version: string;
  created_by: string;
  created_at: string;
}

export interface CopybookTemplate {
  name: string;
  grid_type: 'tianzige' | 'mizige' | 'jiugongge' | 'pinyin' | 'english' | 'blank';
  rows: number;
  cols: number;
  cell_w_mm: number;
  cell_h_mm: number;
  margin_top_mm: number;
  margin_left_mm: number;
  line_style: 'solid' | 'dashed' | 'dotted';
  line_color: string;
  include_mid_guides: boolean;
  extra?: {
    assist_type?: 'mi' | 'jiugong' | 'none';
    page_size?: 'A4' | 'A3';
    watermark?: {
      text: string;
      opacity: number;
      position: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right' | 'center';
    };
    qrcode?: {
      url: string;
      position: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right';
      size_mm: number;
    };
  };
}

export interface CopybookFont {
  name: string;
  file_id: string;
  size_pt: number;
}

export interface CopybookContent {
  text: string;
  repeat_per_char: number;
  pinyin?: string[];
}

export interface CopybookPage {
  text: string;
  title: string;
}

export interface CopybookConfig {
  meta: CopybookMeta;
  template: CopybookTemplate;
  font: CopybookFont;
  content: CopybookContent;
  multi_pages?: CopybookPage[];
}

// 前端状态管理类型
export interface EditorState {
  config: CopybookConfig;
  currentPage: number;
  isPreviewMode: boolean;
  selectedFont: string;
  availableFonts: FontInfo[];
  templates: TemplateInfo[];
}

export interface FontInfo {
  id: string;
  name: string;
  filename: string;
  size: number;
  uploaded_at: string;
}

export interface TemplateInfo {
  id: string;
  name: string;
  description: string;
  preview_url?: string;
  config: Partial<CopybookTemplate>;
}

// API 响应类型
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  message?: string;
}

export interface UploadResponse {
  file_id: string;
  filename: string;
  size: number;
}

// 组件 Props 类型
export interface CopybookCellProps {
  char: string;
  pinyin?: string;
  gridType: CopybookTemplate['grid_type'];
  cellSize: { width: number; height: number };
  fontSize: number;
  fontFamily: string;
  lineColor: string;
  lineStyle: CopybookTemplate['line_style'];
}

export interface TemplatePreviewProps {
  template: CopybookTemplate;
  isSelected: boolean;
  onClick: () => void;
}

export interface FontSelectorProps {
  fonts: FontInfo[];
  selectedFont: string;
  onFontChange: (fontId: string) => void;
  onUploadFont: (file: File) => void;
}

// 工具函数类型
export type GridGenerator = (
  text: string,
  template: CopybookTemplate,
  font: CopybookFont
) => CopybookCellProps[][];

export type PDFExporter = (
  config: CopybookConfig,
  pages?: CopybookPage[]
) => Promise<void>;
