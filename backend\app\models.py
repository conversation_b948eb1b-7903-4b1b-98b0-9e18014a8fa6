from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any, Literal
from datetime import datetime

# 基础模型定义

class CopybookMeta(BaseModel):
    version: str = "1.1"
    created_by: str
    created_at: str

class WatermarkConfig(BaseModel):
    text: str
    opacity: float = Field(ge=0.0, le=1.0)
    position: Literal["top-left", "top-right", "bottom-left", "bottom-right", "center"]

class QRCodeConfig(BaseModel):
    url: str
    position: Literal["top-left", "top-right", "bottom-left", "bottom-right"]
    size_mm: int = Field(ge=5, le=50)

class TemplateExtra(BaseModel):
    assist_type: Optional[Literal["mi", "jiugong", "none"]] = "none"
    page_size: Optional[Literal["A4", "A3"]] = "A4"
    watermark: Optional[WatermarkConfig] = None
    qrcode: Optional[QRCodeConfig] = None

class CopybookTemplate(BaseModel):
    name: str
    grid_type: Literal["tianzige", "mizige", "jiugongge", "pinyin", "english", "blank"]
    rows: int = Field(ge=1, le=20)
    cols: int = Field(ge=1, le=20)
    cell_w_mm: int = Field(ge=10, le=50)
    cell_h_mm: int = Field(ge=10, le=50)
    margin_top_mm: int = Field(ge=5, le=50)
    margin_left_mm: int = Field(ge=5, le=50)
    line_style: Literal["solid", "dashed", "dotted"] = "solid"
    line_color: str = "#CCCCCC"
    include_mid_guides: bool = True
    extra: Optional[TemplateExtra] = None

class CopybookFont(BaseModel):
    name: str
    file_id: str
    size_pt: int = Field(ge=8, le=72)

class CopybookContent(BaseModel):
    text: str
    repeat_per_char: int = Field(ge=1, le=10)
    pinyin: Optional[List[str]] = None

class CopybookPage(BaseModel):
    text: str
    title: str

class CopybookConfig(BaseModel):
    meta: CopybookMeta
    template: CopybookTemplate
    font: CopybookFont
    content: CopybookContent
    multi_pages: Optional[List[CopybookPage]] = None

# API 请求/响应模型

class FontInfo(BaseModel):
    id: str
    name: str
    filename: str
    size: int
    uploaded_at: str

class TemplateInfo(BaseModel):
    id: str
    name: str
    description: str
    preview_url: Optional[str] = None
    config: CopybookTemplate

class ConfigInfo(BaseModel):
    id: str
    name: str
    created_at: str

class UploadResponse(BaseModel):
    file_id: str
    filename: str
    size: int

class ApiResponse(BaseModel):
    success: bool
    data: Optional[Any] = None
    message: Optional[str] = None

# 创建响应的工具函数
def create_success_response(data: Any = None, message: str = None) -> Dict[str, Any]:
    """创建成功响应"""
    return {
        "success": True,
        "data": data,
        "message": message
    }

def create_error_response(message: str, data: Any = None) -> Dict[str, Any]:
    """创建错误响应"""
    return {
        "success": False,
        "data": data,
        "message": message
    }
