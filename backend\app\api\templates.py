from fastapi import APIRouter, HTTPException, Depends
from typing import Dict, Any
from ..services.template_service import TemplateService
from ..storage.file_storage import FileStorage

router = APIRouter(prefix="/templates", tags=["templates"])

# 依赖注入
def get_template_service() -> TemplateService:
    storage = FileStorage()
    return TemplateService(storage)

@router.get("/")
async def get_templates(template_service: TemplateService = Depends(get_template_service)):
    """获取所有模板"""
    return template_service.get_templates()

@router.post("/")
async def create_template(
    template_data: Dict[str, Any],
    template_service: TemplateService = Depends(get_template_service)
):
    """创建新模板"""
    return template_service.save_template(template_data)

@router.get("/{template_id}")
async def get_template(
    template_id: str,
    template_service: TemplateService = Depends(get_template_service)
):
    """获取指定模板"""
    template = template_service.get_template(template_id)
    
    if not template:
        raise HTTPException(status_code=404, detail="模板不存在")
    
    return {
        "success": True,
        "data": template.dict(),
        "message": "获取模板成功"
    }

@router.put("/{template_id}")
async def update_template(
    template_id: str,
    template_data: Dict[str, Any],
    template_service: TemplateService = Depends(get_template_service)
):
    """更新模板"""
    return template_service.update_template(template_id, template_data)

@router.delete("/{template_id}")
async def delete_template(
    template_id: str,
    template_service: TemplateService = Depends(get_template_service)
):
    """删除模板"""
    return template_service.delete_template(template_id)
