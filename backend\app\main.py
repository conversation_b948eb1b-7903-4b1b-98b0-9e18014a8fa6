from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import JSONResponse
import os
from pathlib import Path

from .api import fonts, templates, configs

# 创建 FastAPI 应用
app = FastAPI(
    title="字帖生成器 API",
    description="字帖生成器后端 API 服务",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# CORS 中间件配置
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://localhost:5173"],  # 前端开发服务器
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 静态文件服务（用于字体文件下载）
fonts_dir = Path("fonts")
fonts_dir.mkdir(exist_ok=True)

app.mount("/static/fonts", StaticFiles(directory=str(fonts_dir)), name="fonts")

# 注册 API 路由
app.include_router(fonts.router, prefix="/api")
app.include_router(templates.router, prefix="/api")
app.include_router(configs.router, prefix="/api")

# 根路径
@app.get("/")
async def root():
    """API 根路径"""
    return {
        "message": "字帖生成器 API 服务",
        "version": "1.0.0",
        "docs": "/docs",
        "status": "running"
    }

# 健康检查
@app.get("/health")
async def health_check():
    """健康检查端点"""
    return {
        "status": "healthy",
        "message": "API 服务正常运行"
    }

# 全局异常处理
@app.exception_handler(HTTPException)
async def http_exception_handler(request, exc):
    """HTTP 异常处理器"""
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "success": False,
            "message": exc.detail,
            "status_code": exc.status_code
        }
    )

@app.exception_handler(Exception)
async def general_exception_handler(request, exc):
    """通用异常处理器"""
    return JSONResponse(
        status_code=500,
        content={
            "success": False,
            "message": "服务器内部错误",
            "detail": str(exc) if app.debug else None
        }
    )

# 启动事件
@app.on_event("startup")
async def startup_event():
    """应用启动时执行"""
    print("🚀 字帖生成器 API 服务启动成功")
    print("📚 API 文档: http://localhost:8000/docs")
    print("🔍 ReDoc 文档: http://localhost:8000/redoc")

# 关闭事件
@app.on_event("shutdown")
async def shutdown_event():
    """应用关闭时执行"""
    print("👋 字帖生成器 API 服务已关闭")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
